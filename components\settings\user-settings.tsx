"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useStaff } from "@/lib/staff-provider"
import { Edit, Loader2, MoreHorizontal, Plus, Shield, Trash, UserPlus, MapPin, Building2 } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { StaffAvatar } from "@/components/ui/staff-avatar"
import { useToast } from "@/components/ui/use-toast"
import { useUnifiedStaff } from "@/lib/unified-staff-provider"
import { useLocations } from "@/lib/location-provider"
import { PERMISSIONS, PERMISSION_CATEGORIES } from "@/lib/permissions"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { SettingsStorage, User, Role, Location } from "@/lib/settings-storage"

export function UserSettings() {
  const { toast } = useToast()
  const { users, updateUser, deleteUser, refreshData, syncUserLocations } = useUnifiedStaff()
  const { locations, isLoading: locationsLoading } = useLocations()
  const [searchTerm, setSearchTerm] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [localUsers, setLocalUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false)
  const [isAddRoleDialogOpen, setIsAddRoleDialogOpen] = useState(false)
  const [isEditUserDialogOpen, setIsEditUserDialogOpen] = useState(false)
  const [isEditRoleDialogOpen, setIsEditRoleDialogOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [isDeleteUserDialogOpen, setIsDeleteUserDialogOpen] = useState(false)
  const [isDeleteRoleDialogOpen, setIsDeleteRoleDialogOpen] = useState(false)
  const [userToEdit, setUserToEdit] = useState<User | null>(null)
  const [roleToEdit, setRoleToEdit] = useState<Role | null>(null)
  const [userToDelete, setUserToDelete] = useState<string | null>(null)
  const [roleToDelete, setRoleToDelete] = useState<string | null>(null)

  // Helper function to get location name by ID
  const getLocationName = (locationId: string): string => {
    const location = locations.find(loc => loc.id === locationId)
    return location ? location.name : "Unknown Location"
  }

  // Helper function to get location details for display
  const getLocationDetails = (locationId: string) => {
    const location = locations.find(loc => loc.id === locationId)
    return location ? {
      name: location.name,
      address: location.address,
      status: location.status
    } : null
  }

  // New user form state
  const [newUser, setNewUser] = useState<Partial<User>>({
    name: "",
    email: "",
    role: "staff",
    locations: [],
    status: "Active",
    avatar: "",
    color: "bg-blue-500",
  })

  // New role form state
  const [newRole, setNewRole] = useState<Partial<Role>>({
    name: "",
    description: "",
    permissions: [],
  })

  // Handler functions
  const handleDeleteUser = async (userId: string) => {
    try {
      setIsSubmitting(true)
      await deleteUser(userId)
      toast({
        title: "Success",
        description: "User deleted successfully",
      })
    } catch (error) {
      console.error("Error deleting user:", error)
      toast({
        title: "Error",
        description: "Failed to delete user",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Load data from storage - run only once on mount
  useEffect(() => {
    // Sync user locations with staff locations first (both API and local)
    const syncLocations = async () => {
      try {
        // Call API endpoint for server-side sync
        await fetch('/api/sync-user-locations', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        })

        // Also sync locally
        syncUserLocations()
      } catch (error) {
        console.error('Error syncing user locations:', error)
        // Fallback to local sync only
        syncUserLocations()
      }
    }

    syncLocations()

    // Refresh data to ensure it's up to date
    refreshData()

    // Load roles
    const storedRoles = SettingsStorage.getRoles()
    if (storedRoles.length === 0) {
      // Initialize with default roles if empty
      const initialRoles = [
        {
          id: "super_admin",
          name: "Super Admin",
          description: "Full access to all settings and features across all locations",
          userCount: 1,
          permissions: ["all"],
        },
        {
          id: "org_admin",
          name: "Organization Admin",
          description: "Access to organization-wide settings and features",
          userCount: 2,
          permissions: [
            "manage_locations",
            "manage_staff",
            "manage_services",
            "manage_clients",
            "manage_inventory",
            "manage_reports",
          ],
        },
        {
          id: "location_manager",
          name: "Location Manager",
          description: "Access to settings and features for assigned locations",
          userCount: 3,
          permissions: ["manage_staff", "manage_services", "manage_clients", "manage_inventory", "view_reports"],
        },
        {
          id: "staff",
          name: "Staff",
          description: "Access to appointments, clients, and services",
          userCount: 15,
          permissions: ["view_appointments", "manage_own_appointments", "view_clients", "view_services"],
        },
        {
          id: "receptionist",
          name: "Receptionist",
          description: "Access to appointments, clients, and point of sale",
          userCount: 5,
          permissions: ["view_appointments", "manage_appointments", "view_clients", "manage_clients", "use_pos"],
        },
      ]
      setRoles(initialRoles)
      SettingsStorage.saveRoles(initialRoles)
    } else {
      setRoles(storedRoles)
    }
  }, []) // Empty dependency array - run only once on mount

  // Update user counts in roles when users change
  useEffect(() => {
    const storedRoles = SettingsStorage.getRoles()
    if (storedRoles.length > 0) {
      const rolesWithCounts = storedRoles.map(role => {
        const count = users.filter(user => user.role === role.id).length;
        return { ...role, userCount: count };
      });
      setRoles(rolesWithCounts);
      SettingsStorage.saveRoles(rolesWithCounts);
    }
  }, [users]) // Only depend on users for count updates

  // Filter users based on search term
  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  // Update local users when unified users change
  useEffect(() => {
    setLocalUsers(users)
  }, [users])

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Users & Permissions</h3>
          <p className="text-sm text-muted-foreground">
            Manage user accounts, roles, and permissions for your organization
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setIsAddRoleDialogOpen(true)}>
            <Shield className="mr-2 h-4 w-4" />
            Add Role
          </Button>
          <Button onClick={() => setIsAddUserDialogOpen(true)}>
            <UserPlus className="mr-2 h-4 w-4" />
            Add User
          </Button>
        </div>
      </div>

      <Tabs defaultValue="users" className="w-full">
        <TabsList>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="roles">Roles & Permissions</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>
                Manage user accounts and their access permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2 mb-4">
                <Input
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="max-w-sm"
                />
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Locations</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Login</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          <div className="flex flex-col items-center gap-2">
                            <Shield className="h-8 w-8 text-muted-foreground" />
                            <p className="text-muted-foreground">No users found</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-8 w-8">
                                <AvatarFallback className={user.color}>
                                  {user.avatar}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium">{user.name}</div>
                                <div className="text-sm text-muted-foreground">{user.email}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {roles.find(r => r.id === user.role)?.name || user.role}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {user.locations.map((locationId) => {
                                const location = locations.find(l => l.id === locationId)
                                return (
                                  <Badge key={locationId} variant="outline" className="text-xs">
                                    <MapPin className="mr-1 h-3 w-3" />
                                    {location?.name || locationId}
                                  </Badge>
                                )
                              })}
                              {user.locations.length === 0 && (
                                <span className="text-sm text-muted-foreground">No locations</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={user.status === "Active" ? "default" : "secondary"}>
                              {user.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm text-muted-foreground">
                              {user.lastLogin || "Never"}
                            </span>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => {
                                  setSelectedUser(user)
                                  setIsEditUserDialogOpen(true)
                                }}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit User
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleDeleteUser(user.id)}
                                  className="text-destructive"
                                >
                                  <Trash className="mr-2 h-4 w-4" />
                                  Delete User
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Roles & Permissions</CardTitle>
              <CardDescription>
                Define roles and their associated permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {roles.map((role) => (
                  <Card key={role.id}>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <h4 className="font-medium">{role.name}</h4>
                          <p className="text-sm text-muted-foreground">{role.description}</p>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Shield className="h-4 w-4" />
                            {role.userCount} users
                            <span>•</span>
                            {role.permissions.length} permissions
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedRole(role)
                              setIsEditRoleDialogOpen(true)
                            }}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}