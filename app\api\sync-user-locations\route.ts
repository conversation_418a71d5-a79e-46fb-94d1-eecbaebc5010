import { NextResponse } from "next/server"
import { UnifiedStaffService } from "@/lib/unified-staff-service"

/**
 * POST /api/sync-user-locations
 * 
 * Synchronizes user location assignments with staff location assignments
 * This ensures that user location data matches staff location data
 */
export async function POST() {
  try {
    console.log("🔄 Syncing user locations with staff locations...")

    // Use the unified staff service to sync user locations
    const result = UnifiedStaffService.syncUserLocationsFromStaff()

    console.log(`✅ User location sync completed: ${result.updated} users updated, ${result.errors} errors`)

    return NextResponse.json({
      success: true,
      message: "User locations synchronized successfully",
      stats: {
        usersUpdated: result.updated,
        errors: result.errors
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("❌ Error syncing user locations:", error)
    return NextResponse.json(
      { 
        error: "Failed to sync user locations",
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/sync-user-locations
 * 
 * Get statistics about user-staff location synchronization
 */
export async function GET() {
  try {
    console.log("🔄 Getting user-staff location sync statistics...")

    // Get all users and staff to compare
    const staffData = UnifiedStaffService.getAllStaffWithUserData()
    const userData = UnifiedStaffService.getAllUsersWithStaffData()

    let usersNeedingSync = 0
    let totalUsers = userData.length

    // Check which users need location sync
    userData.forEach(({ user, staff }) => {
      if (staff) {
        const userLocations = user.locations || []
        const staffLocations = staff.locations || []
        
        // Check if locations match
        const locationsMatch = userLocations.length === staffLocations.length &&
          userLocations.every(loc => staffLocations.includes(loc)) &&
          staffLocations.every(loc => userLocations.includes(loc))
        
        if (!locationsMatch) {
          usersNeedingSync++
        }
      }
    })

    return NextResponse.json({
      statistics: {
        totalUsers,
        usersNeedingSync,
        needsSync: usersNeedingSync > 0,
        lastChecked: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error("❌ Error getting user location sync statistics:", error)
    return NextResponse.json(
      { 
        error: "Failed to get sync statistics",
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
