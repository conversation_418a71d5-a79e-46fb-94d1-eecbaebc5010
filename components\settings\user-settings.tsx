"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useStaff } from "@/lib/staff-provider"
import { Edit, Loader2, MoreHorizontal, Plus, Shield, Trash, UserPlus, MapPin, Building2 } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { StaffAvatar } from "@/components/ui/staff-avatar"
import { useToast } from "@/components/ui/use-toast"
import { useUnifiedStaff } from "@/lib/unified-staff-provider"
import { useLocations } from "@/lib/location-provider"
import { PERMISSIONS, PERMISSION_CATEGORIES } from "@/lib/permissions"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { SettingsStorage, User, Role, Location } from "@/lib/settings-storage"

export function UserSettings() {
  const { toast } = useToast()
  const { users, updateUser, deleteUser, refreshData, syncUserLocations } = useUnifiedStaff()
  const { locations, isLoading: locationsLoading } = useLocations()
  const [searchTerm, setSearchTerm] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [localUsers, setLocalUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false)
  const [isAddRoleDialogOpen, setIsAddRoleDialogOpen] = useState(false)
  const [isEditUserDialogOpen, setIsEditUserDialogOpen] = useState(false)
  const [isEditRoleDialogOpen, setIsEditRoleDialogOpen] = useState(false)
  const [isDeleteUserDialogOpen, setIsDeleteUserDialogOpen] = useState(false)
  const [isDeleteRoleDialogOpen, setIsDeleteRoleDialogOpen] = useState(false)
  const [userToEdit, setUserToEdit] = useState<User | null>(null)
  const [roleToEdit, setRoleToEdit] = useState<Role | null>(null)
  const [userToDelete, setUserToDelete] = useState<string | null>(null)
  const [roleToDelete, setRoleToDelete] = useState<string | null>(null)

  // Helper function to get location name by ID
  const getLocationName = (locationId: string): string => {
    const location = locations.find(loc => loc.id === locationId)
    return location ? location.name : "Unknown Location"
  }

  // Helper function to get location details for display
  const getLocationDetails = (locationId: string) => {
    const location = locations.find(loc => loc.id === locationId)
    return location ? {
      name: location.name,
      address: location.address,
      status: location.status
    } : null
  }

  // New user form state
  const [newUser, setNewUser] = useState<Partial<User>>({
    name: "",
    email: "",
    role: "staff",
    locations: [],
    status: "Active",
    avatar: "",
    color: "bg-blue-500",
  })

  // New role form state
  const [newRole, setNewRole] = useState<Partial<Role>>({
    name: "",
    description: "",
    permissions: [],
  })

  // Load data from storage - run only once on mount
  useEffect(() => {
    // Sync user locations with staff locations first (both API and local)
    const syncLocations = async () => {
      try {
        // Call API endpoint for server-side sync
        await fetch('/api/sync-user-locations', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        })

        // Also sync locally
        syncUserLocations()
      } catch (error) {
        console.error('Error syncing user locations:', error)
        // Fallback to local sync only
        syncUserLocations()
      }
    }

    syncLocations()

    // Refresh data to ensure it's up to date
    refreshData()

    // Load roles
    const storedRoles = SettingsStorage.getRoles()
    if (storedRoles.length === 0) {
      // Initialize with default roles if empty
      const initialRoles = [
        {
          id: "super_admin",
          name: "Super Admin",
          description: "Full access to all settings and features across all locations",
          userCount: 1,
          permissions: ["all"],
        },
        {
          id: "org_admin",
          name: "Organization Admin",
          description: "Access to organization-wide settings and features",
          userCount: 2,
          permissions: [
            "manage_locations",
            "manage_staff",
            "manage_services",
            "manage_clients",
            "manage_inventory",
            "manage_reports",
          ],
        },
        {
          id: "location_manager",
          name: "Location Manager",
          description: "Access to settings and features for assigned locations",
          userCount: 3,
          permissions: ["manage_staff", "manage_services", "manage_clients", "manage_inventory", "view_reports"],
        },
        {
          id: "staff",
          name: "Staff",
          description: "Access to appointments, clients, and services",
          userCount: 15,
          permissions: ["view_appointments", "manage_own_appointments", "view_clients", "view_services"],
        },
        {
          id: "receptionist",
          name: "Receptionist",
          description: "Access to appointments, clients, and point of sale",
          userCount: 5,
          permissions: ["view_appointments", "manage_appointments", "view_clients", "manage_clients", "use_pos"],
        },
      ]
      setRoles(initialRoles)
      SettingsStorage.saveRoles(initialRoles)
    } else {
      setRoles(storedRoles)
    }
  }, []) // Empty dependency array - run only once on mount

  // Update user counts in roles when users change
  useEffect(() => {
    const storedRoles = SettingsStorage.getRoles()
    if (storedRoles.length > 0) {
      const rolesWithCounts = storedRoles.map(role => {
        const count = users.filter(user => user.role === role.id).length;
        return { ...role, userCount: count };
      });
      setRoles(rolesWithCounts);
      SettingsStorage.saveRoles(rolesWithCounts);
    }
  }, [users]) // Only depend on users for count updates

  // Filter users based on search term
  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  // Update local users when unified users change
  useEffect(() => {
    setLocalUsers(users)
  }, [users])

  return (
    <div className="space-y-6">
      <h1>User Settings</h1>
      <p>This is a test to see if the JSX works</p>
    </div>
  )
}